<h2 mat-dialog-title *ngIf="poolType === 'static'">
  {{ 'DOMAINS.addStaticPool' | translate }}
</h2>
<h2 mat-dialog-title *ngIf="poolType === 'dynamic'">
  {{ 'DOMAINS.addDynamicPool' | translate }}
</h2>

<mat-dialog-content class="sw-dialog-content">
  <mat-form-field appearance="outline">
    <mat-label>{{ 'DOMAINS.GRID.name' | translate }}</mat-label>
    <input type="text" matInput trimValue [formControl]="nameControl">
    <mat-error *ngIf="nameControl.hasError('required') && nameControl.touched">
      {{ 'VALIDATION.required' | translate }}
    </mat-error>
  </mat-form-field>

  <table mat-table [dataSource]="dataSource">
    <ng-container matColumnDef="code">
      <th mat-header-cell *matHeaderCellDef>
      </th>
      <td mat-cell *matCellDef="let row">
        <mat-checkbox [(ngModel)]="row.selected" (change)="rowSelectedChanged($event, row)">
        </mat-checkbox>
      </td>
    </ng-container>

    <ng-container matColumnDef="name">
      <th mat-header-cell *matHeaderCellDef>
        {{ 'DOMAINS.GRID.domain' | translate }}
      </th>
      <td mat-cell *matCellDef="let row">
        {{ row.displayName }}
      </td>
    </ng-container>

    <ng-container matColumnDef="type">
      <th mat-header-cell *matHeaderCellDef>
        {{ 'DOMAINS.GRID.type' | translate }}
      </th>
      <td mat-cell *matCellDef="let row">
        {{ row.type }}
      </td>
    </ng-container>

    <ng-container matColumnDef="active">
      <th mat-header-cell *matHeaderCellDef>
        {{ 'DOMAINS.GRID.active' | translate }}
      </th>
      <td mat-cell *matCellDef="let row">
        <mat-checkbox [(ngModel)]="row.active"
                      (change)="rowActiveChanged($event, row)">
        </mat-checkbox>
      </td>
    </ng-container>

    <ng-container matColumnDef="amount">
      <td mat-footer-cell *matFooterCellDef colspan="3">
        {{ 'ENTITY_SETUP.REGIONAL.MODALS.selected' | translate }}:
        <span>
          <strong>{{ selectedItems.length }}</strong>
        </span>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    <tr mat-footer-row *matFooterRowDef="['amount']; sticky: true"></tr>
  </table>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button
    mat-button
    color="primary"
    class="mat-button-md"
    mat-dialog-close>
    {{ 'DIALOG.cancel' | translate }}
  </button>
  <button
    mat-flat-button
    color="primary"
    class="mat-button-md"
    cdkFocusInitial
    (click)="submit()">
    {{ 'DIALOG.save' | translate }}
  </button>
</mat-dialog-actions>
