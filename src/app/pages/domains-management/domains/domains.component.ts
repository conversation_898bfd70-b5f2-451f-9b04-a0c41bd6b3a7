import { Directive, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { RowAction, SwuiGridComponent, SwuiGridField, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Domain, DomainType } from 'src/app/common/models/domain.model';
import { DomainsItemDialogComponent } from './domains-item-dialog/domains-item-dialog.component';
import { filter, finalize, switchMap, takeUntil, tap } from 'rxjs/operators';
import { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { DomainsManagementService } from '../domains-management.service';

@Directive()
export abstract class DomainsComponent implements OnInit, OnDestroy {
  readonly abstract schema: SwuiGridField[];
  readonly abstract filterSchema: SwuiGridField[];
  readonly abstract domainType: DomainType;
  readonly rowActions: RowAction[];

  @ViewChild('grid', { static: true }) public grid: SwuiGridComponent<Domain>;

  private readonly destroyed$ = new Subject();

  constructor(
    private service: DomainsManagementService,
    private dialog: MatDialog,
    private notifications: SwuiNotificationsService,
    private translate: TranslateService
  ) {
    this.rowActions = this.setRowActions();
  }

  ngOnInit(): void {
    this.grid.dataSource.requestData = {
      type: this.domainType
    };
  }

  ngOnDestroy() {
    this.destroyed$.next(this.domainType);
    this.destroyed$.complete();
  }

  protected setRowActions(): RowAction[] {
    return [
      {
        title: 'DOMAINS.GRID.editDomain',
        icon: 'edit',
        fn: (item: Domain) => {
          const dialogRef = this.dialog.open(DomainsItemDialogComponent, {
            width: '500px',
            data: {
              domain: item,
              type: this.domainType
            },
            disableClose: true
          });
          dialogRef.afterClosed()
            .pipe(
              filter(data => !!data),
              switchMap((domain: Domain) => this.service.update(item.id, domain, this.domainType)),
              switchMap((domain: Domain) => this.translate.get('DOMAINS.notificationModified', { domain: domain.domain })),
              tap((message) => this.notifications.success(message)),
              finalize(() => this.grid.dataSource.loadData()),
              takeUntil(this.destroyed$)
            )
            .subscribe();
        },
        canActivateFn: () => true,
      },
      {
        title: 'DOMAINS.GRID.removeDomain',
        icon: 'delete',
        fn: (item: Domain) => {
          const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
            width: '500px',
            data: item,
            disableClose: true
          });
          dialogRef.afterClosed()
            .pipe(
              filter(data => !!data),
              switchMap(() => this.service.delete(item.id, this.domainType)),
              switchMap(() => this.translate.get('DOMAINS.notificationRemoved', { domain: item.domain })),
              tap((message) => this.notifications.success(message)),
              finalize(() => this.grid.dataSource.loadData()),
              takeUntil(this.destroyed$)
            )
            .subscribe();
        },
        canActivateFn: () => true,
      },
    ];
  }
}
