import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { EntityAllowedChildDomainsComponent } from './entity-allowed-child-domains.component';
import { EntityDomainService } from 'src/app/pages/domains-management/entity-domain.service';
import { DomainsManagementService } from 'src/app/pages/domains-management/domains-management.service';
import { Entity } from 'src/app/common/typings';

describe('EntityAllowedChildDomainsComponent', () => {
  let component: EntityAllowedChildDomainsComponent;
  let fixture: ComponentFixture<EntityAllowedChildDomainsComponent>;
  let mockEntityDomainService: jasmine.SpyObj<EntityDomainService>;
  let mockDomainsManagementService: jasmine.SpyObj<DomainsManagementService>;
  let mockNotificationsService: jasmine.SpyObj<SwuiNotificationsService>;

  beforeEach(async () => {
    const entityDomainServiceSpy = jasmine.createSpyObj('EntityDomainService', [
      'setAllowedStaticDomainsForChild',
      'resetAllowedStaticDomainsForChild'
    ]);
    const domainsManagementServiceSpy = jasmine.createSpyObj('DomainsManagementService', ['getList']);
    const notificationsServiceSpy = jasmine.createSpyObj('SwuiNotificationsService', ['success']);

    await TestBed.configureTestingModule({
      declarations: [EntityAllowedChildDomainsComponent],
      imports: [
        ReactiveFormsModule,
        MatSelectModule,
        MatFormFieldModule,
        MatChipsModule,
        MatIconModule,
        MatButtonModule,
        MatTooltipModule,
        TranslateModule.forRoot(),
        NoopAnimationsModule
      ],
      providers: [
        { provide: EntityDomainService, useValue: entityDomainServiceSpy },
        { provide: DomainsManagementService, useValue: domainsManagementServiceSpy },
        { provide: SwuiNotificationsService, useValue: notificationsServiceSpy }
      ]
    }).compileComponents();

    mockEntityDomainService = TestBed.inject(EntityDomainService) as jasmine.SpyObj<EntityDomainService>;
    mockDomainsManagementService = TestBed.inject(DomainsManagementService) as jasmine.SpyObj<DomainsManagementService>;
    mockNotificationsService = TestBed.inject(SwuiNotificationsService) as jasmine.SpyObj<SwuiNotificationsService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(EntityAllowedChildDomainsComponent);
    component = fixture.componentInstance;
    
    // Mock entity
    component.entity = {
      path: 'test:entity',
      settings: {
        allowedStaticDomainsForChildId: ['domain1', 'domain2']
      }
    } as Entity;

    // Mock domains list
    mockDomainsManagementService.getList.and.returnValue(of([
      { id: 'domain1', domain: 'example1.com', type: 'static' },
      { id: 'domain2', domain: 'example2.com', type: 'lobby' }
    ] as any));

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load available domains on init', () => {
    expect(mockDomainsManagementService.getList).toHaveBeenCalled();
    expect(component.availableDomains.length).toBe(2);
  });

  it('should load current selection from entity settings', () => {
    expect(component.selectedDomainIds).toEqual(['domain1', 'domain2']);
    expect(component.domainsControl.value).toEqual(['domain1', 'domain2']);
  });
});
