import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiSelectModule } from '@skywind-group/lib-swui';
import { MatChipsModule } from '@angular/material/chips';
import { MatSelectModule } from '@angular/material/select';
import { DomainItemComponent } from './domain-item.component';
import { SelectDomainDialogComponent } from './select-domain-dialog.component';
import { PoolItemComponent } from './pool-item/pool-item.component';
import { SelectPoolDialogComponent } from './select-pool-dialog/select-pool-dialog.component';
import { DomainsPoolService } from '../../../../../domains-management/domains-pool/domains-pool.service';
import { EntityDomainPoolService } from './entity-domain-pool.service';
import { EntityStaticDomainTagsComponent } from './static-tags/entity-static-domain-tags.component';
import { EntityAllowedChildDomainsComponent } from './allowed-child-domains/entity-allowed-child-domains.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatProgressBarModule,
    MatFormFieldModule,
    MatChipsModule,
    MatSelectModule,
    SwuiSelectModule,
  ],
  exports: [
    DomainItemComponent,
    PoolItemComponent,
    EntityStaticDomainTagsComponent,
    EntityAllowedChildDomainsComponent,
  ],
  declarations: [
    SelectDomainDialogComponent,
    SelectPoolDialogComponent,
    DomainItemComponent,
    PoolItemComponent,
    EntityStaticDomainTagsComponent,
    EntityAllowedChildDomainsComponent,
  ],
  providers: [
    DomainsPoolService,
    EntityDomainPoolService
  ]
})
export class ManageDomainsModule {
}
