import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { PanelAction, SwuiNotificationsService } from '@skywind-group/lib-swui';
import { Subject } from 'rxjs';
import { filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { Domain, DOMAIN_TYPES, DomainsItemDialogData, DomainType } from '../../common/models/domain.model';

import { DomainsItemDialogComponent } from './domains/domains-item-dialog/domains-item-dialog.component';
import { DomainsManagementService } from './domains-management.service';
import { MatDialog } from '@angular/material/dialog';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { DomainsPoolDialogComponent, DomainsPoolDialogData } from './domains-pool/dialog/domains-pool-dialog.component';
import { DomainsPoolService } from './domains-pool/domains-pool.service';

type PanelType = 'domain' | 'pool';

@Component({
  selector: 'domains-management',
  templateUrl: './domains-management.component.html',
  providers: [
    DomainsManagementService,
  ],
})
export class DomainsManagementComponent implements OnInit, OnDestroy {
  panelActions: PanelAction[] = [];
  selectedPanel: PanelType = 'pool';
  domainType: DomainType = DOMAIN_TYPES.static;

  private readonly _destroyed$ = new Subject();

  constructor(private readonly service: DomainsManagementService,
    private readonly poolService: DomainsPoolService,
    private readonly dialog: MatDialog,
    private readonly notifications: SwuiNotificationsService,
    private readonly translate: TranslateService) {
  }

  ngOnInit() {
    this.setPanelActions();
  }

  ngOnDestroy() {
    this._destroyed$.next();
    this._destroyed$.complete();
  }

  onTabChange(event: MatTabChangeEvent) {
    if (event.index === 0) {
      this.selectedPanel = 'pool';
    } else if (event.index === 1) {
      this.selectedPanel = 'domain';
      this.domainType = DOMAIN_TYPES.static;
    } else if (event.index === 2) {
      this.selectedPanel = 'domain';
      this.domainType = DOMAIN_TYPES.dynamic;
    }
    this.setPanelActions();
  }

  private setPanelActions() {
    if (this.selectedPanel === 'pool') {
      this.panelActions = [
        this.getPoolPanelAction(DOMAIN_TYPES.static, 'DOMAINS.addStaticPool'),
        this.getPoolPanelAction(DOMAIN_TYPES.dynamic, 'DOMAINS.addDynamicPool'),
      ];
    } else {
      this.panelActions = [{
        title: 'DOMAINS.addDomain',
        color: 'primary',
        icon: 'add',
        actionFn: () => {
          const data: DomainsItemDialogData = {
            type: this.domainType
          };
          const dialogRef = this.dialog.open(DomainsItemDialogComponent, {
            width: '500px',
            data,
            disableClose: true
          });
          dialogRef.afterClosed()
            .pipe(
              filter(domain => !!domain),
              switchMap((domain: Domain) => this.service.create(domain, this.domainType)),
              switchMap((domain: Domain) => this.translate.get('DOMAINS.notificationCreated', { domain: domain.domain })),
              tap(message => this.notifications.success(message)),
              takeUntil(this._destroyed$)
            )
            .subscribe(() => {
              this.service.isGridChanged$.next(this.domainType);
            });
        },
      }];
    }
  }

  private getPoolPanelAction(poolType: DomainType, title: string): PanelAction {
    return {
      title,
      color: 'primary',
      icon: 'add',
      actionFn: () => {
        const data: DomainsPoolDialogData = {
          poolType
        };
        const dialogRef = this.dialog.open(DomainsPoolDialogComponent, {
          width: '500px',
          data,
          disableClose: true
        });
        dialogRef.afterClosed()
          .pipe(
            filter(record => !!record),
            switchMap(record => this.poolService.create(poolType, record)),
            switchMap(pool => this.translate.get('DOMAINS.notificationPoolCreated', { name: pool.name })),
            tap(message => this.notifications.success(message)),
            takeUntil(this._destroyed$)
          )
          .subscribe(() => {
            this.poolService.isGridChanged$.next();
          });
      },
    };
  }
}
