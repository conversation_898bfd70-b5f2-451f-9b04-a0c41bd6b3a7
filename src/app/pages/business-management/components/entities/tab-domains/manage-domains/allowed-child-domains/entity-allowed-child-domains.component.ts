import { Component, Input, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup } from '@angular/forms';
import { first, switchMap } from 'rxjs/operators';
import { SwuiNotificationsService } from '@skywind-group/lib-swui';
import { TranslateService } from '@ngx-translate/core';
import { Entity } from 'src/app/common/typings';
import { EntityDomainService } from 'src/app/pages/domains-management/entity-domain.service';
import { DomainsManagementService } from 'src/app/pages/domains-management/domains-management.service';
import { Domain, DOMAIN_TYPES } from 'src/app/common/models/domain.model';

@Component({
  selector: 'entity-allowed-child-domains',
  templateUrl: './entity-allowed-child-domains.component.html',
  styleUrls: ['./entity-allowed-child-domains.component.scss']
})
export class EntityAllowedChildDomainsComponent implements OnInit {
  @Input() entity: Entity;

  readonly form: FormGroup;
  readonly domainsControl = new FormControl([]);
  availableDomains: Domain[] = [];
  selectedDomainIds: string[] = [];

  constructor(
    fb: FormBuilder,
    private readonly notifications: SwuiNotificationsService,
    private readonly translate: TranslateService,
    private readonly entityDomainService: EntityDomainService,
    private readonly domainsManagementService: DomainsManagementService,
  ) {
    this.form = fb.group({
      domains: this.domainsControl,
    });
  }

  ngOnInit() {
    this.loadAvailableDomains();
    this.loadCurrentSelection();
  }

  private loadAvailableDomains() {
    this.domainsManagementService.getList(DOMAIN_TYPES.static)
      .pipe(first())
      .subscribe((domains: Domain[]) => {
        this.availableDomains = domains;
      });
  }

  private loadCurrentSelection() {
    if (this.entity?.settings?.allowedStaticDomainsForChildId) {
      this.selectedDomainIds = [...this.entity.settings.allowedStaticDomainsForChildId];
      this.domainsControl.setValue(this.selectedDomainIds);
    }
  }

  onDomainSelectionChange(selectedIds: string[]) {
    this.selectedDomainIds = selectedIds;
    this.domainsControl.setValue(selectedIds);
  }

  saveDomains() {
    const domainIds = this.domainsControl.value || [];
    this.entityDomainService.setAllowedStaticDomainsForChild(this.entity.path, domainIds)
      .pipe(
        first(),
        switchMap(() => this.translate.get('ENTITY_SETUP.DOMAINS.allowedChildDomainsSaved'))
      )
      .subscribe(msg => {
        this.notifications.success(msg);
        // Update the entity settings
        if (this.entity.settings) {
          this.entity.settings.allowedStaticDomainsForChildId = domainIds;
        }
      });
  }

  resetDomains() {
    this.entityDomainService.resetAllowedStaticDomainsForChild(this.entity.path)
      .pipe(
        first(),
        switchMap(() => this.translate.get('ENTITY_SETUP.DOMAINS.allowedChildDomainsReset'))
      )
      .subscribe(msg => {
        this.domainsControl.setValue([]);
        this.selectedDomainIds = [];
        this.notifications.success(msg);
        // Update the entity settings
        if (this.entity.settings) {
          this.entity.settings.allowedStaticDomainsForChildId = [];
        }
      });
  }

  getDomainDisplayName(domainId: string): string {
    const domain = this.availableDomains.find(d => d.id === domainId);
    return domain ? domain.domain : domainId;
  }
}
